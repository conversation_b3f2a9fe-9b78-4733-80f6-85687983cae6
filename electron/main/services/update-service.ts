import pkg from 'electron-updater'
import type { BrowserWindow } from 'electron'
import { dialog } from 'electron'
import { type UpdateConfig, getUpdateConfig } from '../config/update-config'

import { moduleErrorHandlers } from '../core/error-handler'
import { deltaUpdateService } from './delta-update-service'

const { autoUpdater } = pkg

/**
 * 更新状态枚举
 */
export enum UpdateStatus {
  IDLE = 'idle',
  CHECKING = 'checking',
  AVAILABLE = 'available',
  NOT_AVAILABLE = 'not-available',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  ERROR = 'error',
}

/**
 * 更新信息接口
 */
export interface UpdateInfo {
  status: UpdateStatus
  version?: string
  releaseNotes?: string
  downloadProgress?: number
  error?: string
}

/**
 * 应用更新服务
 */
export class UpdateService {
  private config: UpdateConfig
  private updateInfo: UpdateInfo = { status: UpdateStatus.IDLE }
  private checkTimer?: NodeJS.Timeout
  private mainWindow?: BrowserWindow
  private preferDeltaUpdate = true

  constructor() {
    this.config = getUpdateConfig()
    this.setupAutoUpdater()
  }

  /**
   * 初始化更新服务
   */
  async initialize(mainWindow: BrowserWindow): Promise<void> {
    this.mainWindow = mainWindow

    if (!this.config.autoUpdate) {
      // 自动更新已禁用
      return
    }

    // 初始化增量更新服务
    if (this.config.deltaUpdate.enabled) {
      await deltaUpdateService.initialize()
    }

    // 配置更新服务器
    if (this.config.updateServerUrl) {
      autoUpdater.setFeedURL({
        provider: 'generic',
        url: this.config.updateServerUrl,
      })
    }

    // 启动时检查更新
    if (this.config.checkOnStartup) {
      setTimeout(() => {
        this.checkForUpdates()
      }, 5000) // 延迟5秒检查，确保应用完全启动
    }

    // 设置定时检查
    if (this.config.checkInterval > 0) {
      this.startPeriodicCheck()
    }
  }

  /**
   * 设置 autoUpdater 事件监听
   */
  private setupAutoUpdater(): void {
    // 配置 autoUpdater
    autoUpdater.autoDownload = this.config.autoDownload
    autoUpdater.allowPrerelease = this.config.allowPrerelease

    // 开发环境强制启用更新检查
    if (process.env.NODE_ENV !== 'production') {
      autoUpdater.forceDevUpdateConfig = true
    }

    // 检查更新开始
    autoUpdater.on('checking-for-update', () => {
      // 正在检查更新...
      this.updateStatus(UpdateStatus.CHECKING)
    })

    // 发现可用更新
    autoUpdater.on('update-available', (info) => {
      // 发现新版本: ${info.version}
      this.updateStatus(UpdateStatus.AVAILABLE, {
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      })

      if (!this.config.autoDownload) {
        this.showUpdateDialog(info)
      }
    })

    // 没有可用更新
    autoUpdater.on('update-not-available', () => {
      // 当前已是最新版本
      this.updateStatus(UpdateStatus.NOT_AVAILABLE)
    })

    // 下载进度
    autoUpdater.on('download-progress', (progress) => {
      const percent = Math.round(progress.percent)
      // 下载进度: ${percent}%
      this.updateStatus(UpdateStatus.DOWNLOADING, {
        downloadProgress: percent,
      })
    })

    // 下载完成
    autoUpdater.on('update-downloaded', (info) => {
      // 更新下载完成: ${info.version}
      this.updateStatus(UpdateStatus.DOWNLOADED, {
        version: info.version,
      })

      if (this.config.silentUpdate) {
        // 静默更新，直接重启
        autoUpdater.quitAndInstall()
      }
      else {
        this.showInstallDialog()
      }
    })

    // 更新错误
    autoUpdater.on('error', (error) => {
      // 更新错误: ${error}
      this.updateStatus(UpdateStatus.ERROR, {
        error: error.message,
      })
      moduleErrorHandlers.main.error(error, 'autoUpdater')
    })
  }

  /**
   * 手动检查更新
   */
  async checkForUpdates(): Promise<void> {
    if (!this.config.autoUpdate) {
      throw new Error('自动更新已禁用')
    }

    try {
      // 如果启用增量更新，先尝试增量更新
      if (this.config.deltaUpdate.enabled && this.preferDeltaUpdate) {
        const deltaResult = await this.checkDeltaUpdate()
        if (deltaResult) {
          moduleErrorHandlers.main.info('使用增量更新', 'checkForUpdates')
          return
        }
      }

      // 回退到全量更新
      moduleErrorHandlers.main.info('使用全量更新', 'checkForUpdates')
      await autoUpdater.checkForUpdatesAndNotify()
    }
    catch (error) {
      // 检查更新失败: ${error}
      this.updateStatus(UpdateStatus.ERROR, {
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 下载更新
   */
  async downloadUpdate(): Promise<void> {
    try {
      await autoUpdater.downloadUpdate()
    }
    catch (error) {
      // 下载更新失败: ${error}
      this.updateStatus(UpdateStatus.ERROR, {
        error: error instanceof Error ? error.message : String(error),
      })
      throw error
    }
  }

  /**
   * 安装更新并重启
   */
  installUpdate(): void {
    autoUpdater.quitAndInstall()
  }

  /**
   * 获取当前更新信息
   */
  getUpdateInfo(): UpdateInfo {
    return { ...this.updateInfo }
  }

  /**
   * 开始定期检查更新
   */
  private startPeriodicCheck(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
    }

    this.checkTimer = setInterval(() => {
      this.checkForUpdates().catch(() => {
        // 定期检查更新失败，忽略错误
      })
    }, this.config.checkInterval)
  }

  /**
   * 停止定期检查更新
   */
  private stopPeriodicCheck(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
      this.checkTimer = undefined
    }
  }

  /**
   * 更新状态
   */
  private updateStatus(status: UpdateStatus, extra: Partial<UpdateInfo> = {}): void {
    this.updateInfo = {
      ...this.updateInfo,
      status,
      ...extra,
    }

    // 通知渲染进程
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('update-status-changed', this.updateInfo)
    }
  }

  /**
   * 显示更新对话框
   */
  private async showUpdateDialog(info: any): Promise<void> {
    const result = await dialog.showMessageBox(this.mainWindow!, {
      type: 'info',
      title: '发现新版本',
      message: `发现新版本 ${info.version}，是否立即下载？`,
      detail: info.releaseNotes || '暂无更新说明',
      buttons: ['立即下载', '稍后提醒', '跳过此版本'],
      defaultId: 0,
      cancelId: 1,
    })

    switch (result.response) {
      case 0: // 立即下载
        this.downloadUpdate()
        break
      case 1: // 稍后提醒
        // 不做任何操作，等待下次检查
        break
      case 2: // 跳过此版本
        // 可以记录跳过的版本，下次不再提醒
        break
    }
  }

  /**
   * 显示安装对话框
   */
  private async showInstallDialog(): Promise<void> {
    const result = await dialog.showMessageBox(this.mainWindow!, {
      type: 'info',
      title: '更新已下载',
      message: '新版本已下载完成，是否立即重启安装？',
      detail: '重启后将自动安装新版本',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0,
      cancelId: 1,
    })

    if (result.response === 0) {
      this.installUpdate()
    }
  }

  /**
   * 检查增量更新
   */
  async checkDeltaUpdate(): Promise<boolean> {
    try {
      // eslint-disable-next-line n/no-missing-require
      const currentVersion = require('../../../package.json').version

      // 获取最新版本信息
      const latestInfo = await this.getLatestVersionInfo()
      if (!latestInfo || latestInfo.version === currentVersion) {
        this.updateStatus(UpdateStatus.NOT_AVAILABLE)
        return true
      }

      // 检查是否可以增量更新
      const deltaInfo = await deltaUpdateService.checkDeltaUpdate(currentVersion, latestInfo.version)
      if (!deltaInfo || !deltaInfo.canDeltaUpdate) {
        return false // 无法增量更新，回退到全量更新
      }

      // 可以增量更新
      this.updateStatus(UpdateStatus.AVAILABLE, {
        version: latestInfo.version,
        releaseNotes: latestInfo.releaseNotes,
      })

      return true
    }
    catch (error) {
      console.error('检查增量更新失败:', error)
      return false
    }
  }

  /**
   * 获取最新版本信息
   */
  private async getLatestVersionInfo(): Promise<{ version: string, releaseNotes?: string } | null> {
    try {
      const response = await fetch(`${this.config.updateServerUrl}/latest.json`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      return await response.json()
    }
    catch (error) {
      console.error('获取最新版本信息失败:', error)
      return null
    }
  }

  /**
   * 设置更新偏好
   */
  setUpdatePreference(preferDelta: boolean): void {
    this.preferDeltaUpdate = preferDelta
  }

  /**
   * 获取增量更新缓存信息
   */
  async getDeltaCacheInfo(): Promise<{ size: number, count: number }> {
    try {
      const size = await deltaUpdateService.getCacheSize()
      return { size, count: 0 } // 简化实现
    }
    catch {
      return { size: 0, count: 0 }
    }
  }

  /**
   * 清理增量更新缓存
   */
  async cleanDeltaCache(): Promise<void> {
    try {
      await deltaUpdateService.cleanCache()
    }
    catch (error) {
      console.error('清理增量更新缓存失败:', error)
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopPeriodicCheck()
    autoUpdater.removeAllListeners()
  }
}

// 导出单例实例
export const updateService = new UpdateService()
