import { app } from 'electron'
import { join } from 'path'
import { createHash } from 'crypto'
import { promises as fs } from 'fs'
import { getUpdateConfig, type DeltaUpdateInfo } from '../config/update-config'
import { moduleErrorHandlers } from '../core/error-handler'

/**
 * 增量更新服务
 * 提供增量更新的检查、下载和应用功能
 */
export class DeltaUpdateService {
  private config = getUpdateConfig().deltaUpdate
  private cacheDir: string

  constructor() {
    this.cacheDir = join(app.getPath('userData'), this.config.cacheDir)
  }

  /**
   * 初始化增量更新服务
   */
  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      console.log('增量更新已禁用')
      return
    }

    try {
      // 创建缓存目录
      await fs.mkdir(this.cacheDir, { recursive: true })
      console.log('增量更新服务初始化完成')
    } catch (error) {
      moduleErrorHandlers.main.error(
        error instanceof Error ? error : String(error),
        'DeltaUpdateService.initialize'
      )
    }
  }

  /**
   * 检查是否可以进行增量更新
   */
  async checkDeltaUpdate(currentVersion: string, targetVersion: string): Promise<DeltaUpdateInfo | null> {
    if (!this.config.enabled) {
      return null
    }

    try {
      const response = await fetch(`${this.config.serverUrl}/check-delta`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentVersion,
          targetVersion,
          platform: process.platform,
          arch: process.arch,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const deltaInfo: DeltaUpdateInfo = await response.json()
      
      // 检查增量包大小是否超过限制
      if (deltaInfo.deltaSize > this.config.maxDeltaSize) {
        console.log(`增量包过大 (${deltaInfo.deltaSize} bytes)，将使用全量更新`)
        return null
      }

      return deltaInfo
    } catch (error) {
      console.error('检查增量更新失败:', error)
      return null
    }
  }

  /**
   * 下载增量包
   */
  async downloadDeltaPatch(deltaInfo: DeltaUpdateInfo, onProgress?: (progress: number) => void): Promise<string> {
    const deltaPath = join(this.cacheDir, `delta-${deltaInfo.currentVersion}-${deltaInfo.targetVersion}.patch`)

    try {
      const response = await fetch(deltaInfo.deltaUrl)
      if (!response.ok) {
        throw new Error(`下载失败: HTTP ${response.status}`)
      }

      const totalSize = parseInt(response.headers.get('content-length') || '0')
      let downloadedSize = 0

      const fileHandle = await fs.open(deltaPath, 'w')
      const reader = response.body?.getReader()

      if (!reader) {
        throw new Error('无法获取响应流')
      }

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          await fileHandle.write(value)
          downloadedSize += value.length

          if (onProgress && totalSize > 0) {
            onProgress((downloadedSize / totalSize) * 100)
          }
        }
      } finally {
        await fileHandle.close()
      }

      // 验证校验和
      const actualChecksum = await this.calculateFileChecksum(deltaPath)
      if (actualChecksum !== deltaInfo.deltaChecksum) {
        await fs.unlink(deltaPath)
        throw new Error('增量包校验失败')
      }

      return deltaPath
    } catch (error) {
      console.error('下载增量包失败:', error)
      throw error
    }
  }

  /**
   * 应用增量包
   */
  async applyDeltaPatch(deltaPath: string, currentAppPath: string): Promise<string> {
    // 这里需要实现具体的增量更新逻辑
    // 可以使用 bsdiff、xdelta 等算法
    // 或者自定义的文件差分算法
    
    try {
      // 示例：简单的文件替换逻辑
      // 实际实现需要根据增量包格式来处理
      const tempAppPath = join(this.cacheDir, 'temp-app')
      
      // 这里应该实现真正的增量更新逻辑
      // 例如：解析增量包，应用文件差异等
      console.log('应用增量包:', deltaPath)
      console.log('当前应用路径:', currentAppPath)
      console.log('临时应用路径:', tempAppPath)
      
      // 返回更新后的应用路径
      return tempAppPath
    } catch (error) {
      console.error('应用增量包失败:', error)
      throw error
    }
  }

  /**
   * 计算文件校验和
   */
  private async calculateFileChecksum(filePath: string): Promise<string> {
    const fileBuffer = await fs.readFile(filePath)
    return createHash('sha256').update(fileBuffer).digest('hex')
  }

  /**
   * 清理缓存
   */
  async cleanCache(): Promise<void> {
    try {
      const files = await fs.readdir(this.cacheDir)
      for (const file of files) {
        await fs.unlink(join(this.cacheDir, file))
      }
      console.log('增量更新缓存已清理')
    } catch (error) {
      console.error('清理缓存失败:', error)
    }
  }

  /**
   * 获取缓存大小
   */
  async getCacheSize(): Promise<number> {
    try {
      const files = await fs.readdir(this.cacheDir)
      let totalSize = 0
      
      for (const file of files) {
        const stat = await fs.stat(join(this.cacheDir, file))
        totalSize += stat.size
      }
      
      return totalSize
    } catch (error) {
      console.error('获取缓存大小失败:', error)
      return 0
    }
  }
}

// 导出单例实例
export const deltaUpdateService = new DeltaUpdateService()
