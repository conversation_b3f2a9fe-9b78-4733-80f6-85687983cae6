import { readFileSync } from 'node:fs'
import { join } from 'node:path'
import { ipcMain } from 'electron'
import { type UpdateInfo, updateService } from '../services/update-service'
/**
 * 更新相关 IPC 处理
 */
export class UpdateIpcHandler {
  /**
   * 注册更新相关的 IPC 处理器
   */
  static register(): void {
    // 检查更新
    ipcMain.handle('update:check', async () => {
      try {
        await updateService.checkForUpdates()
        return { success: true }
      }
      catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        }
      }
    })

    // 下载更新
    ipcMain.handle('update:download', async () => {
      try {
        await updateService.downloadUpdate()
        return { success: true }
      }
      catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        }
      }
    })

    // 安装更新
    ipcMain.handle('update:install', () => {
      updateService.installUpdate()
      return { success: true }
    })

    // 获取更新信息
    ipcMain.handle('update:get-info', (): UpdateInfo => {
      return updateService.getUpdateInfo()
    })

    // 获取应用版本
    ipcMain.handle('update:get-version', () => {
      try {
        // 尝试从环境变量获取版本
        if (process.env.npm_package_version) {
          return process.env.npm_package_version
        }

        // 从package.json文件读取版本
        const packageJsonPath = join(process.cwd(), 'package.json')
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'))
        return packageJson.version || '1.0.0'
      }
      catch {
        // 如果读取失败，返回默认版本
        return '1.0.0'
      }
    })
  }

  /**
   * 注销更新相关的 IPC 处理器
   */
  static unregister(): void {
    ipcMain.removeHandler('update:check')
    ipcMain.removeHandler('update:download')
    ipcMain.removeHandler('update:install')
    ipcMain.removeHandler('update:get-info')
    ipcMain.removeHandler('update:get-version')
  }
}
