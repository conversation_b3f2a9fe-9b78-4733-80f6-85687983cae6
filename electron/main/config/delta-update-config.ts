/**
 * 增量更新配置
 */

export interface DeltaUpdateConfig {
  /** 是否启用增量更新 */
  enableDeltaUpdate: boolean
  /** 增量更新服务器 URL */
  deltaServerUrl: string
  /** 最大增量更新版本跨度 */
  maxDeltaVersions: number
  /** 增量包最大大小（字节） */
  maxDeltaSize: number
  /** 是否回退到全量更新 */
  fallbackToFull: boolean
  /** 增量更新缓存目录 */
  deltaCacheDir: string
}

/**
 * 默认增量更新配置
 */
export const defaultDeltaUpdateConfig: DeltaUpdateConfig = {
  enableDeltaUpdate: true,
  deltaServerUrl: process.env.DELTA_UPDATE_SERVER_URL || 'https://your-delta-server.com',
  maxDeltaVersions: 5, // 最多支持跨5个版本的增量更新
  maxDeltaSize: 50 * 1024 * 1024, // 50MB，超过则使用全量更新
  fallbackToFull: true,
  deltaCacheDir: 'delta-cache',
}

/**
 * 增量更新信息接口
 */
export interface DeltaUpdateInfo {
  /** 当前版本 */
  currentVersion: string
  /** 目标版本 */
  targetVersion: string
  /** 增量包 URL */
  deltaUrl: string
  /** 增量包大小 */
  deltaSize: number
  /** 增量包校验和 */
  deltaChecksum: string
  /** 是否可以增量更新 */
  canDeltaUpdate: boolean
  /** 全量更新 URL（备用） */
  fullUpdateUrl?: string
}

/**
 * 获取增量更新配置
 */
export function getDeltaUpdateConfig(): DeltaUpdateConfig {
  return {
    ...defaultDeltaUpdateConfig,
    // 开发环境可能需要不同的配置
    enableDeltaUpdate: process.env.NODE_ENV === 'production',
  }
}
