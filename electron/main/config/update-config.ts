/**
 * 应用更新配置
 */

export interface UpdateConfig {
  /** 是否启用自动更新 */
  autoUpdate: boolean
  /** 更新服务器 URL */
  updateServerUrl: string
  /** 检查更新间隔（毫秒） */
  checkInterval: number
  /** 是否在启动时检查更新 */
  checkOnStartup: boolean
  /** 是否允许预发布版本 */
  allowPrerelease: boolean
  /** 是否自动下载更新 */
  autoDownload: boolean
  /** 是否在后台静默更新 */
  silentUpdate: boolean

  /** 增量更新配置 */
  deltaUpdate: {
    /** 是否启用增量更新 */
    enabled: boolean
    /** 增量更新服务器 URL */
    serverUrl: string
    /** 最大增量更新版本跨度 */
    maxVersionSpan: number
    /** 增量包最大大小（字节） */
    maxDeltaSize: number
    /** 是否回退到全量更新 */
    fallbackToFull: boolean
    /** 增量更新缓存目录 */
    cacheDir: string
  }

  /** 更新策略 */
  strategy: 'auto' | 'delta' | 'full'
}

/**
 * 默认更新配置
 */
export const defaultUpdateConfig: UpdateConfig = {
  autoUpdate: true,
  updateServerUrl: process.env.UPDATE_SERVER_URL || 'https://your-update-server.com',
  checkInterval: 30 * 60 * 1000, // 30分钟
  checkOnStartup: true,
  allowPrerelease: false,
  autoDownload: true,
  silentUpdate: false,

  // 增量更新配置
  deltaUpdate: {
    enabled: true,
    serverUrl: process.env.DELTA_UPDATE_SERVER_URL || 'https://your-delta-server.com',
    maxVersionSpan: 5, // 最多支持跨5个版本的增量更新
    maxDeltaSize: 50 * 1024 * 1024, // 50MB，超过则使用全量更新
    fallbackToFull: true,
    cacheDir: 'delta-cache',
  },

  // 默认使用智能更新策略
  strategy: 'auto',
}

/**
 * 增量更新信息接口
 */
export interface DeltaUpdateInfo {
  /** 当前版本 */
  currentVersion: string
  /** 目标版本 */
  targetVersion: string
  /** 增量包 URL */
  deltaUrl: string
  /** 增量包大小 */
  deltaSize: number
  /** 增量包校验和 */
  deltaChecksum: string
  /** 是否可以增量更新 */
  canDeltaUpdate: boolean
  /** 全量更新 URL（备用） */
  fullUpdateUrl?: string
}

/**
 * 获取更新配置
 */
export function getUpdateConfig(): UpdateConfig {
  // 可以从配置文件或环境变量中读取配置
  return {
    ...defaultUpdateConfig,
    // 允许通过环境变量控制是否启用自动更新
    // 默认在开发环境中也启用（用于测试），可通过 DISABLE_AUTO_UPDATE=true 禁用
    autoUpdate: process.env.DISABLE_AUTO_UPDATE !== 'true',
    // 开发环境使用测试服务器URL
    updateServerUrl: process.env.UPDATE_SERVER_URL
      || (process.env.NODE_ENV === 'production'
        ? 'https://your-update-server.com/updates'
        : 'http://localhost:3000/updates'),
    // 开发环境缩短检查间隔便于测试
    checkInterval: process.env.NODE_ENV === 'production'
      ? 30 * 60 * 1000 // 生产环境：30分钟
      : 5 * 60 * 1000, // 开发环境：5分钟
    // 开发环境不自动下载，便于测试
    autoDownload: process.env.NODE_ENV === 'production',
    // 增量更新配置
    deltaUpdate: {
      ...defaultUpdateConfig.deltaUpdate,
      enabled: process.env.NODE_ENV === 'production',
    },
  }
}
