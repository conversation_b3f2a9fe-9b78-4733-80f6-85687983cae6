/**
 * 全局类型声明
 * 为渲染进程中的 window 对象添加 IPC API 类型
 */

import type { BusinessIPCInterface } from './index'

declare global {
  interface Window {
    // 优化的 IPC API
    optimizedIPC: {
      // 表示从渲染进程向主进程发送请求并等待响应。
      invoke: <T = any, R = any>(channel: string, data: T, timeout?: number) => Promise<R>
      on: <T = any>(channel: string, listener: (data: T) => void) => void
      off: <T = any>(channel: string, listener?: (data: T) => void) => void
      emit: <T = any>(channel: string, data: T) => void
      handle: <T = any, R = any>(channel: string, handler: (data: T) => Promise<R> | R) => void
      unhandle: (channel: string) => void
      emitToRenderer: <T = any>(channel: string, data: T) => void
      onRenderer: <T = any>(channel: string, listener: (data: T) => void) => void
      offRenderer: <T = any>(channel: string, listener?: (data: T) => void) => void
    }

    // 类型安全的业务 IPC API
    businessIPC: {
      invoke: <K extends keyof BusinessIPCInterface>(
        channel: K,
        data: Parameters<BusinessIPCInterface[K]>[0]
      ) => Promise<ReturnType<BusinessIPCInterface[K]>>
      emit: <K extends keyof BusinessIPCInterface>(
        channel: K,
        data: Parameters<BusinessIPCInterface[K]>[0]
      ) => void
      on: <K extends keyof BusinessIPCInterface>(
        channel: K,
        listener: BusinessIPCInterface[K]
      ) => void
    }

    // 原生 IPC API（兼容性）
    ipcRenderer: {
      on: (...args: any[]) => any
      off: (...args: any[]) => any
      send: (...args: any[]) => any
      invoke: (...args: any[]) => Promise<any>
    }

    // 注意：更新相关 API 通过 optimizedIPC 调用
    // 例如：window.optimizedIPC.invoke('update:check', {})
  }
}

export {}
