/**
 * 前端更新服务
 */

export interface UpdateInfo {
  status: 'idle' | 'checking' | 'available' | 'not-available' | 'downloading' | 'downloaded' | 'error'
  version?: string
  releaseNotes?: string
  downloadProgress?: number
  error?: string
}

export interface UpdateResult {
  success: boolean
  error?: string
}

/**
 * 更新服务类
 */
export class UpdateService {
  private updateInfo: UpdateInfo = { status: 'idle' }
  private listeners: Array<(info: UpdateInfo) => void> = []

  constructor() {
    this.setupIpcListeners()
  }

  /**
   * 设置 IPC 监听器
   */
  private setupIpcListeners(): void {
    if (window.optimizedIPC) {
      // 监听更新状态变化
      window.optimizedIPC.on('update-status-changed', (info: UpdateInfo) => {
        this.updateInfo = info
        this.notifyListeners()
      })
    }
  }

  /**
   * 检查更新
   */
  async checkForUpdates(): Promise<UpdateResult> {
    if (!window.optimizedIPC) {
      return { success: false, error: 'IPC 系统不可用' }
    }

    try {
      const result = await window.optimizedIPC.invoke('update:check', {})
      return result
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 下载更新
   */
  async downloadUpdate(): Promise<UpdateResult> {
    if (!window.optimizedIPC) {
      return { success: false, error: 'IPC 系统不可用' }
    }

    try {
      const result = await window.optimizedIPC.invoke('update:download', {})
      return result
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 安装更新
   */
  async installUpdate(): Promise<UpdateResult> {
    if (!window.optimizedIPC) {
      return { success: false, error: 'IPC 系统不可用' }
    }

    try {
      const result = await window.optimizedIPC.invoke('update:install', {})
      return result
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 获取当前版本
   */
  async getCurrentVersion(): Promise<string> {
    if (!window.optimizedIPC) {
      return '未知版本'
    }

    try {
      return await window.optimizedIPC.invoke('update:get-version', {})
    }
    catch (error) {
      console.error('获取版本失败:', error)
      return '未知版本'
    }
  }

  /**
   * 获取更新信息
   */
  async getUpdateInfo(): Promise<UpdateInfo> {
    if (!window.optimizedIPC) {
      return this.updateInfo
    }

    try {
      const info = await window.optimizedIPC.invoke('update:get-info', {})
      this.updateInfo = info
      return info
    }
    catch (error) {
      console.error('获取更新信息失败:', error)
      return this.updateInfo
    }
  }

  /**
   * 添加状态监听器
   */
  addListener(listener: (info: UpdateInfo) => void): void {
    this.listeners.push(listener)
  }

  /**
   * 移除状态监听器
   */
  removeListener(listener: (info: UpdateInfo) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach((listener) => {
      try {
        listener(this.updateInfo)
      }
      catch (error) {
        console.error('更新监听器执行失败:', error)
      }
    })
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.listeners = []
  }
}

// 导出单例实例
export const updateService = new UpdateService()

// 导出类型（避免重复导出）
export type { UpdateInfo as UpdateInfoType, UpdateResult as UpdateResultType }
