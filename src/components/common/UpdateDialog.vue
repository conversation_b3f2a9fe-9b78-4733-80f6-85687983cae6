<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { NButton, NIcon, NModal, NProgress, NSpin, useMessage } from 'naive-ui'
import { Icon } from '@iconify/vue'
import { type UpdateInfo, updateService } from '@/service/update'

interface Props {
  show?: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
})

const emit = defineEmits<Emits>()

const message = useMessage()

// 响应式数据
const showModal = computed({
  get: () => props.show,
  set: value => emit('update:show', value),
})

const updateInfo = ref<UpdateInfo>({ status: 'idle' })
const currentVersion = ref('未知版本')
const checking = ref(false)
const downloading = ref(false)

// 方法
async function checkUpdate() {
  checking.value = true
  try {
    const result = await updateService.checkForUpdates()
    if (!result.success) {
      message.error(`检查更新失败: ${result.error}`)
    }
  }
  catch (error) {
    message.error(`检查更新失败: ${error}`)
  }
  finally {
    checking.value = false
  }
}

async function downloadUpdate() {
  downloading.value = true
  try {
    const result = await updateService.downloadUpdate()
    if (!result.success) {
      message.error(`下载更新失败: ${result.error}`)
    }
  }
  catch (error) {
    message.error(`下载更新失败: ${error}`)
  }
  finally {
    downloading.value = false
  }
}

async function installUpdate() {
  try {
    await updateService.installUpdate()
  }
  catch (error) {
    message.error(`安装更新失败: ${error}`)
  }
}

function closeDialog() {
  showModal.value = false
}

function updateListener(info: UpdateInfo) {
  updateInfo.value = info
}

// 生命周期
onMounted(async () => {
  // 获取当前版本
  currentVersion.value = await updateService.getCurrentVersion()

  // 获取当前更新信息
  updateInfo.value = await updateService.getUpdateInfo()

  // 添加更新状态监听器
  updateService.addListener(updateListener)
})

onUnmounted(() => {
  // 移除更新状态监听器
  updateService.removeListener(updateListener)
})
</script>

<template>
  <NModal
    v-model:show="showModal"
    :mask-closable="false"
    :close-on-esc="false"
    preset="dialog"
    title="应用更新"
    :style="{ width: '480px' }"
  >
    <div class="update-dialog">
      <!-- 检查更新状态 -->
      <div v-if="updateInfo.status === 'checking'" class="status-section">
        <div class="flex items-center gap-3">
          <NSpin size="small" />
          <span>正在检查更新...</span>
        </div>
      </div>

      <!-- 发现新版本 -->
      <div v-else-if="updateInfo.status === 'available'" class="status-section">
        <div class="mb-4">
          <h4 class="mb-2 text-lg font-semibold">
            发现新版本 {{ updateInfo.version }}
          </h4>
          <div v-if="updateInfo.releaseNotes" class="release-notes">
            <h5 class="mb-2 font-medium">
              更新内容：
            </h5>
            <div class="max-h-32 overflow-y-auto rounded bg-gray-50 p-3 text-sm">
              <pre class="whitespace-pre-wrap">{{ updateInfo.releaseNotes }}</pre>
            </div>
          </div>
        </div>
        <div class="flex gap-2">
          <NButton type="primary" :loading="downloading" @click="downloadUpdate">
            立即下载
          </NButton>
          <NButton @click="closeDialog">
            稍后提醒
          </NButton>
        </div>
      </div>

      <!-- 下载中 -->
      <div v-else-if="updateInfo.status === 'downloading'" class="status-section">
        <div class="mb-4">
          <div class="mb-2 flex items-center justify-between">
            <span>正在下载更新...</span>
            <span class="text-sm text-gray-500">{{ updateInfo.downloadProgress || 0 }}%</span>
          </div>
          <NProgress
            type="line"
            :percentage="updateInfo.downloadProgress || 0"
            :show-indicator="false"
            status="info"
          />
        </div>
      </div>

      <!-- 下载完成 -->
      <div v-else-if="updateInfo.status === 'downloaded'" class="status-section">
        <div class="mb-4">
          <div class="mb-3 flex items-center gap-2">
            <NIcon size="20" color="#52c41a">
              <Icon icon="mdi:check-circle" />
            </NIcon>
            <span class="font-medium">更新已下载完成</span>
          </div>
          <p class="mb-4 text-sm text-gray-600">
            新版本已下载完成，点击"立即重启"安装更新。
          </p>
        </div>
        <div class="flex gap-2">
          <NButton type="primary" @click="installUpdate">
            立即重启
          </NButton>
          <NButton @click="closeDialog">
            稍后重启
          </NButton>
        </div>
      </div>

      <!-- 没有更新 -->
      <div v-else-if="updateInfo.status === 'not-available'" class="status-section">
        <div class="mb-3 flex items-center gap-2">
          <NIcon size="20" color="#52c41a">
            <Icon icon="mdi:check-circle" />
          </NIcon>
          <span>当前已是最新版本</span>
        </div>
        <div class="flex justify-end">
          <NButton @click="closeDialog">
            确定
          </NButton>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="updateInfo.status === 'error'" class="status-section">
        <div class="mb-4">
          <div class="mb-3 flex items-center gap-2">
            <NIcon size="20" color="#ff4d4f">
              <Icon icon="mdi:close-circle" />
            </NIcon>
            <span class="text-red-600 font-medium">更新失败</span>
          </div>
          <p class="mb-4 text-sm text-gray-600">
            {{ updateInfo.error || '未知错误' }}
          </p>
        </div>
        <div class="flex gap-2">
          <NButton type="primary" :loading="checking" @click="checkUpdate">
            重试
          </NButton>
          <NButton @click="closeDialog">
            取消
          </NButton>
        </div>
      </div>

      <!-- 版本信息 -->
      <div class="version-info mt-4 border-t border-gray-200 pt-4">
        <div class="text-xs text-gray-500">
          当前版本：{{ currentVersion }}
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.update-dialog {
  padding: 16px 0;
}

.status-section {
  min-height: 80px;
}

.release-notes pre {
  font-family: inherit;
  margin: 0;
}

.version-info {
  text-align: center;
}
</style>
