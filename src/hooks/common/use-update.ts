import { onMounted, onUnmounted, ref } from 'vue'
import { useMessage } from 'naive-ui'
import { type UpdateInfo, updateService } from '@/service/update'

/**
 * 更新管理 Hook
 */
export function useUpdate() {
  const message = useMessage()

  // 响应式数据
  const updateInfo = ref<UpdateInfo>({ status: 'idle' })
  const currentVersion = ref('未知版本')
  const showUpdateDialog = ref(false)
  const isChecking = ref(false)
  const isDownloading = ref(false)

  // 更新状态监听器
  const updateListener = (info: UpdateInfo) => {
    updateInfo.value = info

    // 根据状态自动显示对话框
    if (info.status === 'available' || info.status === 'downloaded') {
      showUpdateDialog.value = true
    }

    // 显示状态消息
    switch (info.status) {
      case 'not-available':
        message.success('当前已是最新版本')
        break
      case 'error':
        message.error(`更新失败: ${info.error}`)
        break
      case 'downloaded':
        message.success('更新下载完成，可以重启安装')
        break
    }
  }

  // 检查更新
  const checkForUpdates = async (showMessage = true) => {
    if (isChecking.value)
      return

    isChecking.value = true
    try {
      const result = await updateService.checkForUpdates()
      if (!result.success) {
        if (showMessage) {
          message.error(`检查更新失败: ${result.error}`)
        }
        return false
      }
      return true
    }
    catch (error) {
      if (showMessage) {
        message.error(`检查更新失败: ${error}`)
      }
      return false
    }
    finally {
      isChecking.value = false
    }
  }

  // 下载更新
  const downloadUpdate = async () => {
    if (isDownloading.value)
      return

    isDownloading.value = true
    try {
      const result = await updateService.downloadUpdate()
      if (!result.success) {
        message.error(`下载更新失败: ${result.error}`)
        return false
      }
      return true
    }
    catch (error) {
      message.error(`下载更新失败: ${error}`)
      return false
    }
    finally {
      isDownloading.value = false
    }
  }

  // 安装更新
  const installUpdate = async () => {
    try {
      await updateService.installUpdate()
      return true
    }
    catch (error) {
      message.error(`安装更新失败: ${error}`)
      return false
    }
  }

  // 获取当前版本
  const getCurrentVersion = async () => {
    try {
      currentVersion.value = await updateService.getCurrentVersion()
      return currentVersion.value
    }
    catch (error) {
      console.error('获取版本失败:', error)
      return '未知版本'
    }
  }

  // 获取更新信息
  const getUpdateInfo = async () => {
    try {
      updateInfo.value = await updateService.getUpdateInfo()
      return updateInfo.value
    }
    catch (error) {
      console.error('获取更新信息失败:', error)
      return updateInfo.value
    }
  }

  // 显示更新对话框
  const showDialog = () => {
    showUpdateDialog.value = true
  }

  // 隐藏更新对话框
  const hideDialog = () => {
    showUpdateDialog.value = false
  }

  // 初始化
  const initialize = async () => {
    // 获取当前版本
    await getCurrentVersion()

    // 获取当前更新信息
    await getUpdateInfo()

    // 添加更新状态监听器
    updateService.addListener(updateListener)
  }

  // 清理
  const cleanup = () => {
    updateService.removeListener(updateListener)
  }

  // 生命周期管理
  onMounted(() => {
    initialize()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    // 响应式数据
    updateInfo: readonly(updateInfo),
    currentVersion: readonly(currentVersion),
    showUpdateDialog,
    isChecking: readonly(isChecking),
    isDownloading: readonly(isDownloading),

    // 方法
    checkForUpdates,
    downloadUpdate,
    installUpdate,
    getCurrentVersion,
    getUpdateInfo,
    showDialog,
    hideDialog,
    initialize,
    cleanup,
  }
}

/**
 * 全局更新管理 Hook
 * 用于在应用级别管理更新状态
 */
export function useGlobalUpdate() {
  const updateHook = useUpdate()

  // 自动检查更新（静默）
  const autoCheckUpdate = async () => {
    await updateHook.checkForUpdates(false)
  }

  // 手动检查更新（显示消息）
  const manualCheckUpdate = async () => {
    await updateHook.checkForUpdates(true)
  }

  return {
    ...updateHook,
    autoCheckUpdate,
    manualCheckUpdate,
  }
}
