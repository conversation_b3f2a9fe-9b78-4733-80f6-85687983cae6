<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="更新策略配置" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <div class="h-full flex-col-stretch gap-12px">
        <!-- 更新方式选择 -->
        <div class="strategy-section">
          <h3 class="text-lg font-semibold mb-4">更新方式</h3>
          <div class="bg-gray-50 p-4 rounded-lg">
            <NRadioGroup v-model:value="updateStrategy" @update:value="handleStrategyChange">
              <div class="space-y-3">
                <NRadio value="auto" class="flex items-start">
                  <div class="ml-2">
                    <div class="font-medium">智能更新（推荐）</div>
                    <div class="text-sm text-gray-600">
                      自动选择最优更新方式：小版本使用增量更新，大版本使用全量更新
                    </div>
                  </div>
                </NRadio>
                
                <NRadio value="delta" class="flex items-start">
                  <div class="ml-2">
                    <div class="font-medium">优先增量更新</div>
                    <div class="text-sm text-gray-600">
                      优先使用增量更新，下载量小、速度快，无法增量时自动回退到全量更新
                    </div>
                  </div>
                </NRadio>
                
                <NRadio value="full" class="flex items-start">
                  <div class="ml-2">
                    <div class="font-medium">仅全量更新</div>
                    <div class="text-sm text-gray-600">
                      始终使用全量更新，稳定可靠，但下载量较大
                    </div>
                  </div>
                </NRadio>
              </div>
            </NRadioGroup>
          </div>
        </div>

        <!-- 增量更新设置 -->
        <div v-if="updateStrategy !== 'full'" class="delta-settings-section">
          <h3 class="text-lg font-semibold mb-4">增量更新设置</h3>
          <div class="bg-white border rounded-lg p-4 space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">最大增量包大小</div>
                <div class="text-sm text-gray-600">超过此大小将使用全量更新</div>
              </div>
              <div class="flex items-center gap-2">
                <NInputNumber
                  v-model:value="maxDeltaSize"
                  :min="1"
                  :max="500"
                  :step="1"
                  style="width: 100px"
                />
                <span class="text-sm text-gray-500">MB</span>
              </div>
            </div>
            
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">最大版本跨度</div>
                <div class="text-sm text-gray-600">支持跨越的最大版本数</div>
              </div>
              <div class="flex items-center gap-2">
                <NInputNumber
                  v-model:value="maxVersionSpan"
                  :min="1"
                  :max="20"
                  :step="1"
                  style="width: 100px"
                />
                <span class="text-sm text-gray-500">个版本</span>
              </div>
            </div>
            
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">缓存管理</div>
                <div class="text-sm text-gray-600">增量更新缓存占用: {{ formatSize(cacheInfo.size) }}</div>
              </div>
              <div class="flex gap-2">
                <NButton size="small" @click="refreshCacheInfo">
                  <template #icon>
                    <NIcon><RefreshOutlined /></NIcon>
                  </template>
                  刷新
                </NButton>
                <NButton size="small" type="warning" @click="clearCache">
                  <template #icon>
                    <NIcon><DeleteOutlined /></NIcon>
                  </template>
                  清理缓存
                </NButton>
              </div>
            </div>
          </div>
        </div>

        <!-- 更新对比 -->
        <div class="comparison-section">
          <h3 class="text-lg font-semibold mb-4">更新方式对比</h3>
          <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-300">
              <thead>
                <tr class="bg-gray-50">
                  <th class="border border-gray-300 px-4 py-2 text-left">特性</th>
                  <th class="border border-gray-300 px-4 py-2 text-center">增量更新</th>
                  <th class="border border-gray-300 px-4 py-2 text-center">全量更新</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="border border-gray-300 px-4 py-2">下载大小</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-green-600">小（通常 < 10MB）</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-orange-600">大（完整安装包）</td>
                </tr>
                <tr>
                  <td class="border border-gray-300 px-4 py-2">更新速度</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-green-600">快</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-orange-600">慢</td>
                </tr>
                <tr>
                  <td class="border border-gray-300 px-4 py-2">稳定性</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-orange-600">较好</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-green-600">最好</td>
                </tr>
                <tr>
                  <td class="border border-gray-300 px-4 py-2">兼容性</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-orange-600">有限制</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-green-600">无限制</td>
                </tr>
                <tr>
                  <td class="border border-gray-300 px-4 py-2">网络要求</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-green-600">低</td>
                  <td class="border border-gray-300 px-4 py-2 text-center text-orange-600">高</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <div class="flex gap-3">
            <NButton type="primary" @click="saveSettings" :loading="saving">
              <template #icon>
                <NIcon><SaveOutlined /></NIcon>
              </template>
              保存设置
            </NButton>
            
            <NButton @click="resetSettings">
              <template #icon>
                <NIcon><ReloadOutlined /></NIcon>
              </template>
              重置默认
            </NButton>
            
            <NButton @click="testUpdate" :loading="testing">
              <template #icon>
                <NIcon><ExperimentOutlined /></NIcon>
              </template>
              测试更新
            </NButton>
          </div>
        </div>
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { NCard, NRadioGroup, NRadio, NInputNumber, NButton, NIcon, useMessage } from 'naive-ui'
import { 
  RefreshOutlined, 
  DeleteOutlined, 
  SaveOutlined, 
  ReloadOutlined, 
  ExperimentOutlined 
} from '@vicons/antd'

defineOptions({
  name: 'UpdateStrategy',
})

const message = useMessage()

// 响应式数据
const updateStrategy = ref<'auto' | 'delta' | 'full'>('auto')
const maxDeltaSize = ref(50) // MB
const maxVersionSpan = ref(5)
const cacheInfo = ref({ size: 0, count: 0 })
const saving = ref(false)
const testing = ref(false)

// 方法
const handleStrategyChange = (value: 'auto' | 'delta' | 'full') => {
  console.log('更新策略变更:', value)
}

const refreshCacheInfo = async () => {
  try {
    // 这里应该调用 IPC 获取缓存信息
    // const info = await window.optimizedIPC.invoke('update:get-cache-info', {})
    // cacheInfo.value = info
    
    // 模拟数据
    cacheInfo.value = { size: Math.random() * 100 * 1024 * 1024, count: 3 }
    message.success('缓存信息已刷新')
  } catch (error) {
    message.error('获取缓存信息失败')
  }
}

const clearCache = async () => {
  try {
    // 这里应该调用 IPC 清理缓存
    // await window.optimizedIPC.invoke('update:clear-cache', {})
    
    cacheInfo.value = { size: 0, count: 0 }
    message.success('缓存已清理')
  } catch (error) {
    message.error('清理缓存失败')
  }
}

const saveSettings = async () => {
  saving.value = true
  try {
    // 这里应该调用 IPC 保存设置
    // await window.optimizedIPC.invoke('update:save-strategy', {
    //   strategy: updateStrategy.value,
    //   maxDeltaSize: maxDeltaSize.value * 1024 * 1024,
    //   maxVersionSpan: maxVersionSpan.value,
    // })
    
    message.success('设置已保存')
  } catch (error) {
    message.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  updateStrategy.value = 'auto'
  maxDeltaSize.value = 50
  maxVersionSpan.value = 5
  message.info('已重置为默认设置')
}

const testUpdate = async () => {
  testing.value = true
  try {
    // 这里应该调用 IPC 测试更新
    // await window.optimizedIPC.invoke('update:test', {})
    
    message.success('更新测试完成')
  } catch (error) {
    message.error('更新测试失败')
  } finally {
    testing.value = false
  }
}

const formatSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  refreshCacheInfo()
})
</script>

<style scoped>
.card-wrapper {
  @apply h-full;
}

.strategy-section,
.delta-settings-section,
.comparison-section,
.action-section {
  @apply flex-shrink-0;
}

table {
  font-size: 14px;
}

th, td {
  min-width: 120px;
}
</style>
