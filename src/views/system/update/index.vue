<script setup lang="ts">
import { NButton, NCard, NIcon, NProgress, NTag, useMessage } from 'naive-ui'
import { Icon } from '@iconify/vue'
import { useUpdate } from '@/hooks/common/use-update'
import UpdateDialog from '@/components/common/UpdateDialog.vue'

defineOptions({
  name: 'SystemUpdate',
})

const message = useMessage()

// 使用更新 Hook
const {
  updateInfo,
  currentVersion,
  showUpdateDialog,
  isChecking,
  isDownloading,
  checkForUpdates,
  downloadUpdate,
  installUpdate,
  showDialog,
} = useUpdate()

// 处理检查更新
async function handleCheckUpdate() {
  await checkForUpdates()
}

// 处理下载更新
async function handleDownloadUpdate() {
  await downloadUpdate()
}

// 处理安装更新
async function handleInstallUpdate() {
  const result = await installUpdate()
  if (result) {
    message.success('正在重启应用安装更新...')
  }
}

// 获取状态标签类型
function getStatusTagType(status: string) {
  switch (status) {
    case 'checking':
      return 'info'
    case 'available':
      return 'warning'
    case 'downloading':
      return 'info'
    case 'downloaded':
      return 'success'
    case 'not-available':
      return 'success'
    case 'error':
      return 'error'
    default:
      return 'default'
  }
}

// 获取状态文本
function getStatusText(status: string) {
  switch (status) {
    case 'idle':
      return '待检查'
    case 'checking':
      return '检查中'
    case 'available':
      return '有新版本'
    case 'not-available':
      return '已是最新'
    case 'downloading':
      return '下载中'
    case 'downloaded':
      return '下载完成'
    case 'error':
      return '更新失败'
    default:
      return '未知状态'
  }
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="应用更新管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <div class="h-full flex-col-stretch gap-12px">
        <!-- 当前版本信息 -->
        <div class="version-section">
          <h3 class="mb-4 text-lg font-semibold">
            版本信息
          </h3>
          <div class="rounded-lg bg-gray-50 p-4">
            <div class="mb-2 flex items-center justify-between">
              <span class="font-medium">当前版本：</span>
              <span class="text-blue-600">{{ currentVersion }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="font-medium">更新状态：</span>
              <NTag :type="getStatusTagType(updateInfo.status)">
                {{ getStatusText(updateInfo.status) }}
              </NTag>
            </div>
          </div>
        </div>

        <!-- 更新操作 -->
        <div class="action-section">
          <h3 class="mb-4 text-lg font-semibold">
            更新操作
          </h3>
          <div class="flex flex-wrap gap-3">
            <NButton
              type="primary"
              :loading="isChecking"
              @click="handleCheckUpdate"
            >
              <template #icon>
                <NIcon><Icon icon="mdi:refresh" /></NIcon>
              </template>
              检查更新
            </NButton>

            <NButton
              v-if="updateInfo.status === 'available'"
              type="success"
              :loading="isDownloading"
              @click="handleDownloadUpdate"
            >
              <template #icon>
                <NIcon><Icon icon="mdi:download" /></NIcon>
              </template>
              下载更新
            </NButton>

            <NButton
              v-if="updateInfo.status === 'downloaded'"
              type="warning"
              @click="handleInstallUpdate"
            >
              <template #icon>
                <NIcon><Icon icon="mdi:upload" /></NIcon>
              </template>
              安装更新
            </NButton>

            <NButton
              @click="showDialog"
            >
              <template #icon>
                <NIcon><Icon icon="mdi:cog" /></NIcon>
              </template>
              更新设置
            </NButton>
          </div>
        </div>

        <!-- 更新详情 -->
        <div v-if="updateInfo.status !== 'idle'" class="detail-section">
          <h3 class="mb-4 text-lg font-semibold">
            更新详情
          </h3>
          <div class="border rounded-lg bg-white p-4">
            <!-- 可用更新 -->
            <div v-if="updateInfo.status === 'available' && updateInfo.version">
              <div class="mb-3">
                <span class="font-medium">新版本：</span>
                <span class="text-green-600">{{ updateInfo.version }}</span>
              </div>
              <div v-if="updateInfo.releaseNotes">
                <span class="font-medium">更新内容：</span>
                <div class="mt-2 max-h-40 overflow-y-auto rounded bg-gray-50 p-3 text-sm">
                  <pre class="whitespace-pre-wrap">{{ updateInfo.releaseNotes }}</pre>
                </div>
              </div>
            </div>

            <!-- 下载进度 -->
            <div v-else-if="updateInfo.status === 'downloading'">
              <div class="mb-3">
                <div class="mb-2 flex items-center justify-between">
                  <span class="font-medium">下载进度：</span>
                  <span class="text-sm text-gray-500">{{ updateInfo.downloadProgress || 0 }}%</span>
                </div>
                <NProgress
                  type="line"
                  :percentage="updateInfo.downloadProgress || 0"
                  status="info"
                />
              </div>
            </div>

            <!-- 下载完成 -->
            <div v-else-if="updateInfo.status === 'downloaded'">
              <div class="text-green-600 font-medium">
                ✓ 更新已下载完成，可以安装
              </div>
            </div>

            <!-- 错误信息 -->
            <div v-else-if="updateInfo.status === 'error'">
              <div class="text-red-600">
                <span class="font-medium">错误信息：</span>
                <span>{{ updateInfo.error || '未知错误' }}</span>
              </div>
            </div>

            <!-- 无更新 -->
            <div v-else-if="updateInfo.status === 'not-available'">
              <div class="text-green-600 font-medium">
                ✓ 当前已是最新版本
              </div>
            </div>
          </div>
        </div>

        <!-- 更新历史 -->
        <div class="history-section">
          <h3 class="mb-4 text-lg font-semibold">
            更新说明
          </h3>
          <div class="rounded-lg bg-blue-50 p-4">
            <ul class="text-sm space-y-2">
              <li>• 应用会自动检查更新，发现新版本时会提示用户</li>
              <li>• 可以手动点击"检查更新"按钮主动检查</li>
              <li>• 下载完成后需要重启应用来安装更新</li>
              <li>• 更新过程中请不要关闭应用程序</li>
            </ul>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 更新对话框 -->
    <UpdateDialog v-model:show="showUpdateDialog" />
  </div>
</template>

<style scoped>
.card-wrapper {
  @apply h-full;
}

.version-section,
.action-section,
.detail-section,
.history-section {
  @apply flex-shrink-0;
}

.detail-section pre {
  font-family: inherit;
  margin: 0;
}
</style>
