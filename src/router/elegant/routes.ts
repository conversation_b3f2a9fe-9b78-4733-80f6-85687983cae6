/* prettier-ignore */
/* eslint-disable */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router
import type { GeneratedRoute } from '@elegant-router/types'

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: '首页',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: '系统管理',
      icon: 'carbon:cloud-service-management',
      order: 9
    },
    children: [
      {
        name: 'manage_dept',
        path: '/manage/dept',
        component: 'view.manage_dept',
        meta: {
          title: '部门管理',
          icon: 'carbon:tree-view'
        }
      },
      {
        name: 'manage_dict',
        path: '/manage/dict',
        component: 'view.manage_dict',
        meta: {
          title: '字典管理',
          icon: 'material-symbols:dictionary'
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: '菜单管理',
          icon: 'material-symbols:menu',
          order: 3,
          keepAlive: true
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: '角色管理',
          icon: 'carbon:user-role',
          order: 2
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: '用户管理',
          icon: 'ic:round-manage-accounts',
          order: 1
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        meta: {
          title: '用户详情',
          hideInMenu: true,
          activeMenu: 'manage_user'
        }
      }
    ]
  },
  {
    name: 'monitor',
    path: '/monitor',
    component: 'layout.base',
    meta: {
      title: '系统监控',
      icon: 'eos-icons:monitoring',
      order: 8
    },
    children: [
      {
        name: 'monitor_login-logs',
        path: '/monitor/login-logs',
        component: 'view.monitor_login-logs',
        meta: {
          title: '登录日志',
          icon: 'pajamas:log'
        }
      },
      {
        name: 'monitor_online-user',
        path: '/monitor/online-user',
        component: 'view.monitor_online-user',
        meta: {
          title: '在线用户',
          icon: 'streamline:user-sync-online-in-person'
        }
      },
      {
        name: 'monitor_operation-logs',
        path: '/monitor/operation-logs',
        component: 'view.monitor_operation-logs',
        meta: {
          title: '操作日志',
          icon: 'icon-park-outline:online-meeting'
        }
      },
      {
        name: 'monitor_system-logs',
        path: '/monitor/system-logs',
        component: 'view.monitor_system-logs',
        meta: {
          title: '系统日志',
          icon: 'tdesign:system-log'
        }
      }
    ]
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system'
    },
    children: [
      {
        name: 'system_update',
        path: '/system/update',
        component: 'view.system_update',
        meta: {
          title: 'system_update'
        }
      }
    ]
  }
];
