# Electron 应用自动更新功能使用指南

本项目已集成 `electron-updater` 实现应用的自动更新功能。

## 功能特性

- ✅ 自动检查更新
- ✅ 手动检查更新
- ✅ 下载进度显示
- ✅ 更新对话框
- ✅ 版本信息显示
- ✅ 错误处理
- ✅ 静默更新选项
- ✅ 预发布版本支持

## 架构说明

### 主进程模块

1. **UpdateService** (`electron/main/services/update-service.ts`)
   - 核心更新服务，处理所有更新逻辑
   - 监听 electron-updater 事件
   - 管理更新状态和进度

2. **UpdateConfig** (`electron/main/config/update-config.ts`)
   - 更新配置管理
   - 支持环境变量配置
   - 开发/生产环境区分

3. **UpdateIpcHandler** (`electron/main/ipc/update-ipc.ts`)
   - IPC 通信处理器
   - 暴露更新相关 API 给渲染进程

### 渲染进程模块

1. **UpdateService** (`src/service/update.ts`)
   - 前端更新服务
   - 封装 IPC 调用
   - 状态管理

2. **useUpdate Hook** (`src/hooks/common/use-update.ts`)
   - Vue 组合式 API
   - 响应式状态管理
   - 生命周期管理

3. **UpdateDialog** (`src/components/common/UpdateDialog.vue`)
   - 更新对话框组件
   - 用户交互界面
   - 进度显示

## 使用方法

### 1. 基本使用

```vue
<script setup>
import { useUpdate } from '@/hooks/common/use-update'
import UpdateDialog from '@/components/common/UpdateDialog.vue'

const {
  checkForUpdates,
  showUpdateDialog: showDialog
} = useUpdate()

async function checkUpdate() {
  await checkForUpdates()
}
</script>

<template>
  <div>
    <NButton @click="checkUpdate">
      检查更新
    </NButton>
    <UpdateDialog v-model:show="showDialog" />
  </div>
</template>
```

### 2. 在应用中集成

在主布局或根组件中添加更新对话框：

```vue
<script setup>
import { useGlobalUpdate } from '@/hooks/common/use-update'
import UpdateDialog from '@/components/common/UpdateDialog.vue'

const { showUpdateDialog, autoCheckUpdate } = useGlobalUpdate()

// 应用启动时自动检查更新
onMounted(() => {
  autoCheckUpdate()
})
</script>

<template>
  <div id="app">
    <!-- 应用内容 -->
    <router-view />

    <!-- 更新对话框 -->
    <UpdateDialog v-model:show="showUpdateDialog" />
  </div>
</template>
```

### 3. 手动控制更新流程

```typescript
import { updateService } from '@/service/update'

// 检查更新
const result = await updateService.checkForUpdates()
if (result.success) {
  console.log('检查更新成功')
}

// 下载更新
await updateService.downloadUpdate()

// 安装更新（会重启应用）
await updateService.installUpdate()

// 获取当前版本
const version = await updateService.getCurrentVersion()

// 监听更新状态
updateService.addListener((info) => {
  console.log('更新状态:', info.status)
})
```

## 配置说明

### 1. 更新服务器配置

在 `electron/main/config/update-config.ts` 中配置：

```typescript
export const defaultUpdateConfig: UpdateConfig = {
  autoUpdate: true,
  updateServerUrl: 'https://your-update-server.com',
  checkInterval: 30 * 60 * 1000, // 30分钟
  checkOnStartup: true,
  allowPrerelease: false,
  autoDownload: true,
  silentUpdate: false,
}
```

### 2. 环境变量配置

```bash
# .env
UPDATE_SERVER_URL=https://your-update-server.com
```

### 3. electron-builder 配置

在 `electron-builder.json` 中配置发布信息：

```json
{
  "publish": {
    "provider": "generic",
    "url": "https://your-update-server.com"
  }
}
```

## 更新服务器搭建

### 1. 简单的静态文件服务器

```
your-update-server.com/
├── latest-mac.yml          # macOS 更新信息
├── latest.yml              # Windows 更新信息
├── latest-linux.yml        # Linux 更新信息
├── YourApp-1.0.1.dmg       # macOS 安装包
├── YourApp-1.0.1.exe       # Windows 安装包
└── YourApp-1.0.1.AppImage  # Linux 安装包
```

### 2. 与后端 API 集成

```typescript
// 在 update-config.ts 中
export const defaultUpdateConfig: UpdateConfig = {
  updateServerUrl: 'https://api.yourapp.com/updates',
  // 其他配置...
}
```

后端 API 需要返回符合 electron-updater 格式的更新信息。

## 构建和发布

### 1. 构建应用

```bash
# 开发环境构建
pnpm build-electron:dev

# 生产环境构建
pnpm build-electron

# 构建并发布
pnpm build-electron --publish=always
```

### 2. 发布流程

1. 更新 `package.json` 中的版本号
2. 运行构建命令
3. 将生成的文件上传到更新服务器
4. 更新服务器上的 `latest*.yml` 文件

## 开发调试

### 1. 禁用自动更新

在开发环境中，自动更新默认是禁用的。如需测试，可以修改配置：

```typescript
// electron/main/config/update-config.ts
export function getUpdateConfig(): UpdateConfig {
  return {
    ...defaultUpdateConfig,
    autoUpdate: true, // 强制启用
  }
}
```

### 2. 测试更新流程

1. 构建一个低版本的应用
2. 搭建本地更新服务器
3. 构建高版本应用并放到服务器
4. 运行低版本应用测试更新

## 注意事项

1. **代码签名**: 生产环境需要对应用进行代码签名
2. **HTTPS**: 更新服务器建议使用 HTTPS
3. **版本号**: 确保版本号遵循语义化版本规范
4. **测试**: 充分测试更新流程，避免用户数据丢失
5. **回滚**: 准备回滚方案，以防新版本有问题

## 故障排除

### 1. 更新检查失败

- 检查网络连接
- 验证更新服务器 URL
- 查看控制台错误信息

### 2. 下载失败

- 检查服务器文件是否存在
- 验证文件权限
- 检查磁盘空间

### 3. 安装失败

- 检查应用权限
- 确保应用未被其他进程占用
- 查看系统日志

## API 参考

详细的 API 文档请参考各模块的 TypeScript 类型定义和注释。
