#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * 更新功能测试脚本
 * 用于测试 electron-updater 的基本功能
 */

const { execSync } = require('node:child_process')
const fs = require('node:fs')

console.log('🚀 开始测试更新功能...\n')

// 检查必要的文件是否存在
const requiredFiles = [
  'electron/main/services/update-service.ts',
  'electron/main/config/update-config.ts',
  'electron/main/ipc/update-ipc.ts',
  'src/service/update.ts',
  'src/hooks/common/use-update.ts',
  'src/components/common/UpdateDialog.vue',
]

console.log('📁 检查文件结构...')
let allFilesExist = true

requiredFiles.forEach((file) => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`)
  }
  else {
    console.log(`❌ ${file} - 文件不存在`)
    allFilesExist = false
  }
})

if (!allFilesExist) {
  console.log('\n❌ 部分文件缺失，请检查文件结构')
  process.exit(1)
}

// 检查依赖是否安装
console.log('\n📦 检查依赖...')
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))

if (packageJson.dependencies['electron-updater']) {
  console.log(`✅ electron-updater: ${packageJson.dependencies['electron-updater']}`)
}
else {
  console.log('❌ electron-updater 未安装')
  console.log('请运行: pnpm install electron-updater')
  process.exit(1)
}

// 检查 electron-builder 配置
console.log('\n⚙️  检查 electron-builder 配置...')
if (fs.existsSync('electron-builder.json')) {
  const builderConfig = JSON.parse(fs.readFileSync('electron-builder.json', 'utf8'))

  if (builderConfig.publish) {
    console.log('✅ publish 配置存在')
    console.log(`   Provider: ${builderConfig.publish.provider}`)
    console.log(`   URL: ${builderConfig.publish.url}`)
  }
  else {
    console.log('⚠️  publish 配置不存在，自动更新可能无法正常工作')
  }
}
else {
  console.log('❌ electron-builder.json 不存在')
}

// 检查 TypeScript 编译
console.log('\n🔧 检查 TypeScript 编译...')
try {
  execSync('npx tsc --noEmit', { stdio: 'pipe' })
  console.log('✅ TypeScript 编译通过')
}
catch (error) {
  console.log('❌ TypeScript 编译失败')
  console.log(error.stdout?.toString() || error.message)
}

// 生成测试用的更新配置
console.log('\n📝 生成测试配置...')
const testConfig = {
  updateServerUrl: 'http://localhost:3000/updates',
  autoUpdate: true,
  checkOnStartup: false,
  autoDownload: false,
  silentUpdate: false,
}

console.log('测试配置:')
console.log(JSON.stringify(testConfig, null, 2))

// 提供测试建议
console.log('\n💡 测试建议:')
console.log('1. 修改 electron/main/config/update-config.ts 中的配置')
console.log('2. 搭建本地测试服务器 (可使用 http-server)')
console.log('3. 构建两个不同版本的应用进行测试')
console.log('4. 测试各种更新场景:')
console.log('   - 有新版本可用')
console.log('   - 无新版本')
console.log('   - 网络错误')
console.log('   - 下载进度')
console.log('   - 安装更新')

console.log('\n✅ 更新功能测试准备完成!')
console.log('\n下一步:')
console.log('1. 运行 pnpm dev 启动开发服务器')
console.log('2. 在应用中测试更新功能')
console.log('3. 查看控制台输出和网络请求')
